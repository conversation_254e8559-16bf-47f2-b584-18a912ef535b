<template>
  <div>
    <MLicensePermissionChecker
      :permission="$constants.APPLICATION_DATABASE_CLOUD_LICENSE_PERMISSION"
    >
      <MButton
        v-if="
          monitor.category === 'Network' && monitor.vendor === 'Cisco Systems'
        "
        title="Add WAN Link"
        variant="neutral-lightest"
        class="ml-2 squared-button"
        @click="handleMountDrilldown"
      >
        <MIcon name="cisco-wan-link" />
      </MButton>

      <FlotoDrawerForm
        v-if="mountDrilldown"
        :open="isDrawerOpen"
        width="40%"
        @cancel="handleCancel"
        @submit="handleFormSubmit"
        @reset="resetForm"
      >
        <template v-slot:header> Add WAN Link </template>
        <MRow class="my-2">
          <MCol :size="12">
            <MRadioGroup
              v-model="formData.wanLinkConfigurationType"
              :disabled="disabled"
              as-button
              :options="wlanLinkConfigurationOptions"
            />
          </MCol>
        </MRow>
        <MRow>
          <CredentialProvider
            :search-params="credentialSearchParams"
            @loaded="setCredentialProfiles"
          >
            <MCol :size="10">
              <FlotoFormItem
                id="credential-id"
                label="Credential Profiles"
                :info-tooltip="$message('discovery_wan_link_credential_help')"
                rules="required"
              >
                <CredentialPicker
                  v-model="formData.credentials"
                  allow-create
                  :available-protocols="['SNMP V1/V2c', 'SNMP V3']"
                  :protocol-filter="['SNMP V1/V2c', 'SNMP V3']"
                  :default-form-data="{
                    credentialProfileProtocol: 'SNMP V1/V2c',
                  }"
                />
              </FlotoFormItem>
            </MCol>
          </CredentialProvider>
        </MRow>
        <MRow v-if="formData.wanLinkConfigurationType === 'bulkWANLink'">
          <MCol :size="6">
            <FlotoFormItem
              id="csv-select-id"
              rules="required"
              label="CSV"
              validation-label="CSV"
            >
              <FileDropper
                id="csv-upload"
                v-model="formData.csv"
                mode="attachment"
                button-text="Upload CSV"
                as-link
                :allowed-extensions="['csv']"
                :multiple="false"
                :max-files="1"
              />
              <div id="sample-csv-download-id" class="text-right">
                <a
                  href="/samples/ipsla-rediscovery-sample.csv"
                  target="_blank"
                  class="flex align-center justify-end"
                >
                  <MIcon name="download" class="mr-2 mt-2" />
                  Sample CSV
                </a>
              </div>
            </FlotoFormItem>
          </MCol>
        </MRow>
        <MRow v-if="formData.wanLinkConfigurationType === 'singleWANLink'">
          <MCol :size="6">
            <FlotoFormItem label="WAN Probe" rules="required">
              <FlotoDropdownPicker
                v-model="formData.wanProbe"
                placeholder="Select WAN Probe"
                allow-clear
                :options="wanProbeOptions"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.internetServiceProvider"
              :disabled="disabled"
              label="Internet Service Provider"
              rules="required"
              placeholder="Enter"
            />
          </MCol>
        </MRow>

        <MRow v-if="formData.wanLinkConfigurationType === 'singleWANLink'">
          <MCol :size="6">
            <FlotoFormItem label="Source Interface">
              <DependencyInterfacePicker
                v-model="formData.sourceInterface"
                placeholder="Select Source Interface"
                :additional-keys-to-emit="['ipAddress']"
                :monitor="monitor.ip"
                :filter="wanLinkFilter"
                :transform-fn="transformFn"
              />
            </FlotoFormItem>
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.sourceRouterLocation"
              :disabled="disabled"
              label="Source Router Location"
              placeholder="Enter"
            />
          </MCol>
        </MRow>

        <MRow v-if="formData.wanLinkConfigurationType === 'singleWANLink'">
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.destinationIP"
              :disabled="disabled"
              label="Destination IP"
              rules="required|ip"
              validation-label="IP"
              placeholder="Enter"
              name="ip-host"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.destinationRouterLocation"
              :disabled="disabled"
              label="Destination Router Location"
              placeholder="Enter"
            />
          </MCol>
        </MRow>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.timeout"
              :disabled="disabled"
              label="Timeout"
              rules="numeric"
            />
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full">
          <MCol :size="12">
            <h4 class="text-primary flex items-center my-2">
              IP SLA Operations Test Parameters
            </h4>
          </MCol>
        </MRow>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.payload"
              :disabled="disabled"
              label="Payload"
              placeholder="Enter"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.serviceType"
              :disabled="disabled"
              label="Type of service"
              placeholder="Enter"
            />
          </MCol>
        </MRow>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.frequency"
              :disabled="disabled"
              label="Frequency"
              placeholder="Enter"
              :rules="frequencyValidation"
            />
          </MCol>

          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.operationTimeout"
              :disabled="disabled"
              label="Operation Timeout"
              rules="numeric|required|min_value:10"
            />
          </MCol>
        </MRow>

        <template v-slot:actions="{ submit, reset }">
          <span class="mandatory">
            <span class="text-secondary-red">*</span> fields are mandatory</span
          >
          <MButton
            id="reset-btn-id"
            variant="default"
            class="mr-2"
            @click="reset"
            >Reset</MButton
          >
          <MButton id="submit-btn" :loading="processing" @click="submit"
            >Add WAN Link</MButton
          >
        </template>
      </FlotoDrawerForm>
    </MLicensePermissionChecker>
  </div>
</template>

<script>
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import FileDropper from '@components/file-dropper.vue'
import DependencyInterfacePicker from '@src/modules/settings/ai/components/dependency-interface-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import {
  wanLinkFilter,
  wanLinkTransformer,
} from '@modules/settings/ai/dependency-mapper-api'
import Bus from '@utils/emitter'
import {
  transformWANLinkForServer,
  wanProbeOptions,
} from '@src/components/rediscover-results/rediscover-api'
export default {
  name: 'WANLinkForm',
  components: {
    CredentialPicker,
    FileDropper,
    DependencyInterfacePicker,
    CredentialProvider,
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    monitor: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.wlanLinkConfigurationOptions = [
      { value: 'singleWANLink', text: 'Single WAN Link Configuration' },
      { value: 'bulkWANLink', text: 'Bulk WAN Link Configuration' },
    ]
    this.wanProbeOptions = wanProbeOptions
    this.credentialSearchParams = {
      key: 'credential.profile.protocol',
      value: [
        this.$constants.SNMP_V12,
        this.$constants.SNMP_V3,
        this.$constants.SSH,
        // this.$constants.NCM_TELNET,
      ],
    }
    this.wanLinkFilter = wanLinkFilter
    this.transformFn = wanLinkTransformer
    return {
      formData: {
        wanLinkConfigurationType: 'singleWANLink',
        wanProbe: 'ipslaicmpecho',
      },
      mountDrilldown: false,
      isDrawerOpen: false,
      credentialProfiles: [],
      processing: false,
    }
  },
  computed: {
    frequencyValidation() {
      return {
        required: true,
        numeric: true,
        min_value: Number(this.formData.operationTimeout)
          ? Math.max(this.formData.operationTimeout, 10)
          : 10,
      }
    },
  },
  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleCancel() {
      this.isDrawerOpen = false
      this.formData = {
        wanLinkConfigurationType: 'singleWANLink',
        wanProbe: 'ipslaicmpecho',
      }
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
    resetForm() {
      this.formData = {
        wanLinkConfigurationType: 'singleWANLink',
        wanProbe: 'ipslaicmpecho',
      }
    },
    handleFormSubmit() {
      this.processing = true
      Bus.$emit('server:event', {
        'event.type': this.$constants.WAN_REDISCOVER_EVENT,
        'event.context': {
          id: this.monitor.id,
          'object.vendor': this.monitor.vendor,
          ...transformWANLinkForServer(this.formData),
        },
      })
      this.processing = false
      this.handleCancel()
    },
    setCredentialProfiles(options) {
      this.credentialProfiles = Array.from(options.values())
    },
  },
}
</script>
