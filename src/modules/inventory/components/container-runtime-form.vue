<template>
  <div>
    <MLicensePermissionChecker
      :permission="$constants.APPLICATION_DATABASE_CLOUD_LICENSE_PERMISSION"
    >
      <MButton
        v-if="monitor.category === 'Server' && monitor.type === 'Linux'"
        title="Container Runtime"
        variant="neutral-lightest"
        class="ml-2 squared-button"
        @click="handleMountDrilldown"
      >
        <MIcon name="container-runtime" />
      </MButton>

      <FlotoDrawerForm
        v-if="mountDrilldown"
        :open="isDrawerOpen"
        width="40%"
        @cancel="handleCancel"
        @submit="handleFormSubmit"
        @reset="resetForm"
      >
        <template v-slot:header> Discover Container Runtime </template>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              id="container-runtime-id"
              label="Container Runtime"
              rules="required"
            >
              <FlotoDropdownPicker
                v-model="formData.containerRuntime"
                placeholder="Select Container Runtime"
                :options="containerRuntimeOptions"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow>
          <CredentialProvider
            :search-params="credentialSearchParams"
            @loaded="setCredentialProfiles"
          >
            <MCol :size="10">
              <FlotoFormItem
                id="credential-id"
                label="Credential Profiles"
                rules="required"
              >
                <CredentialPicker
                  v-model="formData.credentials"
                  allow-create
                  :available-protocols="[$constants.HTTP_HTTPS]"
                  :protocol-filter="[$constants.HTTP_HTTPS]"
                  :default-form-data="{
                    credentialProfileProtocol: $constants.HTTP_HTTPS,
                    authenticationType: 'basic',
                  }"
                />
              </FlotoFormItem>
            </MCol>
          </CredentialProvider>
        </MRow>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem label="Tags">
              <LooseTags
                v-model="formData.tags"
                variant="default"
                placeholder="Tags"
                rounded
                :full-width="true"
                always-text-mode
                user-tag-only
                title="Tag"
                class="w-full"
              />
            </FlotoFormItem>
          </MCol>
        </MRow>

        <MRow :gutter="0" class="w-full">
          <MCol :size="12">
            <h4 class="text-primary flex items-center my-2">
              Discovery Parameters
            </h4>
          </MCol>
        </MRow>

        <MRow>
          <MCol :size="6">
            <FlotoFormItem
              v-model="formData.port"
              label="Port"
              rules="required|numeric"
            />
          </MCol>
          <MCol :size="6">
            <FlotoFormItem
              label="Discover Available Containers"
              :info-tooltip="$message('discover_available_containers')"
            >
              <MRow>
                <MCol>
                  <MSwitch
                    id="auto-sync-id"
                    v-model="formData.discoverAllContainers"
                    :checked="formData.discoverAllContainers"
                    checked-children="ON"
                    un-checked-children="OFF"
                  />
                </MCol>
              </MRow>
            </FlotoFormItem>

            <!-- <div class="flex items-center h-full">
            <span class="mr-2">Discover Down Containers</span>
            <MSwitch v-model="formData.discoverAllContainers" />
          </div> -->
          </MCol>
        </MRow>

        <MRow>
          <MCol :size="12">
            <div class="flex items-center mt-4">
              <span class="text-neutral-light">For more information: </span>
              <a
                href="https://docs.motadata.com/motadata-aiops-docs/Adding%20and%20Managing%20Devices/Adding-container-devices-for-monitoring"
                class="text-primary ml-2 flex items-center"
                target="_blank"
              >
                Discover Docker <MIcon name="external-link" class="ml-1" />
              </a>
            </div>
          </MCol>
        </MRow>

        <template v-slot:actions="{ submit, reset }">
          <span class="mandatory">
            <span class="text-secondary-red">*</span> fields are mandatory</span
          >
          <MButton
            id="reset-btn-id"
            variant="default"
            class="mr-2"
            @click="reset"
            >Reset</MButton
          >
          <MButton id="submit-btn" :loading="processing" @click="submit"
            >Save & Run</MButton
          >
        </template>
      </FlotoDrawerForm>
    </MLicensePermissionChecker>
  </div>
</template>

<script>
import CredentialPicker from '@components/data-picker/credential-picker.vue'
import CredentialProvider from '@components/data-provider/credential-provider.vue'
import Bus from '@utils/emitter'
import LooseTags from '@components/loose-tags.vue'
import {
  transformContainerForServer,
  containerRuntimeOptions,
} from '@src/components/rediscover-results/rediscover-api'

export default {
  name: 'ContainerRuntimeForm',
  components: {
    CredentialPicker,
    CredentialProvider,
    LooseTags,
  },
  props: {
    open: {
      type: Boolean,
      default: false,
    },
    monitor: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    this.credentialSearchParams = {
      key: 'credential.profile.protocol',
      value: [this.$constants.HTTP_HTTPS],
    }
    this.containerRuntimeOptions = containerRuntimeOptions
    return {
      formData: {
        containerRuntime: 'docker',
        port: 2375,
      },
      mountDrilldown: false,
      isDrawerOpen: false,
      credentialProfiles: [],
      processing: false,
    }
  },
  methods: {
    handleMountDrilldown() {
      this.mountDrilldown = true
      this.$nextTick(() => {
        this.isDrawerOpen = true
      })
    },
    handleCancel() {
      this.isDrawerOpen = false
      this.formData = {
        containerRuntime: 'docker',
        port: 2375,
      }
      setTimeout(() => {
        this.mountDrilldown = false
      }, 400)
    },
    resetForm() {
      this.formData = {
        containerRuntime: 'docker',
        port: 2375,
      }
    },
    handleFormSubmit() {
      this.processing = true
      Bus.$emit('server:event', {
        'event.type': this.$constants.CONTAINER_REDISCOVER_EVENT,
        'event.context': {
          id: this.monitor.id,
          'object.vendor': this.monitor.vendor,
          ...transformContainerForServer(this.formData),
        },
      })
      this.processing = false
      this.handleCancel()
    },
    setCredentialProfiles(options) {
      this.credentialProfiles = Array.from(options.values())
    },
  },
}
</script>
