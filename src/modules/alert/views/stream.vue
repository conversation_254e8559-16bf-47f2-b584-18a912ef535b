<template>
  <MonitorProvider :search-params="monitorSearchParams">
    <NetRouteProvider>
      <PolicyProvider :type="tab">
        <GroupProvider>
          <MPersistedColumns
            v-model="columns"
            :default-value="columns"
            :module-key="`${tab}-${view}-alert-stream`"
            :available-columns="availableColumns"
          >
            <template
              v-slot="{
                columns: persistedColumns,
                setColumns: updatePersistedColumns,
              }"
            >
              <FlotoFixedView>
                <div class="flex flex-col h-full flex-1 content-inner-panel">
                  <FlotoPageHeader
                    title="Alert"
                    use-divider
                    :back-link="
                      $currentModule.getRoute('dashboard', {
                        query: $route.query,
                      })
                    "
                  >
                    <template v-slot:before-title>
                      <MIcon name="alert" class="text-primary-alt mr-2" />
                    </template>
                    <template v-slot:title>
                      <div
                        v-if="shouldShowModule"
                        class="flex flex-1 items-center justify-end"
                      >
                        <div
                          v-if="view !== 'live' || tab !== 'metric'"
                          class="custom-time-select px-2 radius-corner flex items-center"
                        >
                          <TimeRangePicker v-model="timeline" />
                        </div>
                        <!-- <MRadioGroup
                          v-if="tab === 'metric'"
                          v-model="view"
                          as-button
                          :options="viewOptions"
                        /> -->
                      </div>
                      <div
                        :class="[
                          ...(shouldShowModule
                            ? ['inline-flex', 'ml-2']
                            : [
                                'flex',
                                'flex-1',
                                'items-center',
                                'justify-end',
                              ]),
                        ]"
                      >
                        <template v-if="shouldShowModule">
                          <MButton
                            v-if="view === 'live'"
                            class="squared-button mr-2"
                            variant="neutral-lightest"
                            title="Refresh"
                            @click="refreshGrid"
                          >
                            <MIcon name="sync" class="excluded-header-icon" />
                          </MButton>
                          <MButton
                            class="squared-button mr-2"
                            variant="neutral-lightest"
                            title="Export"
                            @click="exportCsv"
                          >
                            <MIcon
                              name="export-csv"
                              class="excluded-header-icon"
                            />
                          </MButton>
                          <ColumnSelector
                            v-model="columns"
                            :columns="availableColumns"
                            @change="updatePersistedColumns"
                          >
                            <template v-slot:trigger="{ toggle }">
                              <MButton
                                class="squared-button mr-2"
                                variant="neutral-lightest"
                                @click="toggle"
                              >
                                <MIcon
                                  name="eye"
                                  class="excluded-header-icon"
                                />
                              </MButton>
                            </template>
                          </ColumnSelector>
                          <MButton
                            id="filter-btn"
                            class="squared-button"
                            variant="neutral-lightest"
                            @click="isFilterVisible = !isFilterVisible"
                          >
                            <MIcon name="filter" class="excluded-header-icon" />
                          </MButton>
                        </template>

                        <MButton
                          shape="circle"
                          title=" Dashboard"
                          class="squared-button ml-2"
                          variant="neutral-lightest"
                          @click="navigateToDashboard"
                        >
                          <MIcon
                            name="widget-view"
                            class="excluded-header-icon"
                          />
                        </MButton>
                      </div>
                    </template>
                  </FlotoPageHeader>
                  <div class="flex border-bot">
                    <div class="flex-1 h-full items-end flex ml-2">
                      <MTab
                        class="flex-1 no-border"
                        :value="tab"
                        @change="handleTabChange"
                      >
                        <MTabPane key="metric" tab="Metric" />
                        <MTabPane
                          v-if="hasLogFlowPermission"
                          key="log"
                          tab="Log"
                        />
                        <MTabPane
                          v-if="hasLogFlowPermission"
                          key="flow"
                          tab="Flow"
                        />
                        <MTabPane key="trap" tab="Trap" />
                        <MTabPane v-if="hasNetRoutePermission" key="netroute">
                          <template v-slot:tab>
                            <MPopover
                              overlay-class-name="readable-content-overlay picker-overlay no-padding top-110"
                              trigger="hover"
                              placement="bottomLeft"
                              transition-name="slide-up"
                            >
                              <template v-slot:trigger>
                                <div class="flex items-center">
                                  NetRoute
                                  <MTag
                                    :closable="false"
                                    class="cursor-auto mx-2 tag-primary flex items-center justify-center p-1"
                                    variant="primary"
                                  >
                                    BETA
                                  </MTag>
                                </div>
                              </template>

                              <div
                                class="dropdown-list"
                                style="min-width: 250px"
                              >
                                <div
                                  v-for="(item, index) in [
                                    'netroute-source-to-destination',
                                    'netroute-hop-to-hop',
                                  ]"
                                  :key="`${item}-${index}`"
                                  class="list-item cursor-pointer"
                                  :class="{
                                    selected: nestedTabId === item,
                                  }"
                                  @click="handleTabChange('netroute', item)"
                                >
                                  {{
                                    item === 'netroute-source-to-destination'
                                      ? 'Source To Destination'
                                      : 'Hop To Hop'
                                  }}
                                </div>
                              </div>
                            </MPopover>
                          </template>
                        </MTabPane>
                      </MTab>
                    </div>
                  </div>
                  <div class="flex flex-1 min-h-0 flex-col">
                    <div v-if="shouldShowModule" class="px-4 my-2">
                      <Filters
                        v-if="isFilterVisible"
                        v-model="filters"
                        :category="tab"
                        @change="applyFilter"
                        @hide="isFilterVisible = !isFilterVisible"
                      />
                    </div>
                    <div
                      v-if="
                        (category ||
                          policyType ||
                          drillDownSeverity ||
                          drillDownObjectType) &&
                        tab === 'metric' &&
                        shouldShowModule
                      "
                      :size="12"
                      class="px-4 rounded bg-neutral-lightest mb-3"
                    >
                      <div class="flex items-center flex-1" :gutter="0">
                        <div class="flex flex-1">
                          <span class="pr-4 bord-r mr-4 pt-2 pb-2">
                            Applied Filter
                          </span>
                          <span
                            v-if="category"
                            class="filter-alert-tag pt-2 pb-2"
                          >
                            <MTag rounded @close="handleClear">
                              {{ category }}
                            </MTag>
                          </span>
                          <span
                            v-if="policyType"
                            class="filter-alert-tag pt-2 pb-2"
                          >
                            <MTag rounded @close="policyType = undefined">
                              {{ policyType }}
                            </MTag>
                          </span>
                          <span
                            v-if="drillDownSeverity"
                            class="filter-alert-tag pt-2 pb-2"
                          >
                            <MTag
                              rounded
                              @close="drillDownSeverity = undefined"
                            >
                              {{ drillDownSeverity }}
                            </MTag>
                          </span>
                          <span
                            v-if="drillDownObjectType"
                            class="filter-alert-tag pt-2 pb-2"
                          >
                            <MTag
                              rounded
                              @close="drillDownObjectType = undefined"
                            >
                              {{ drillDownObjectType }}
                            </MTag>
                          </span>
                        </div>
                        <div>
                          <a @click="handleClear">Clear All</a>
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="
                        drillDownSeverity &&
                        ['log', 'flow', 'trap', 'netroute'].includes(tab) &&
                        shouldShowModule
                      "
                      :size="12"
                      class="px-4 rounded bg-neutral-lightest mb-3"
                    >
                      <div class="flex items-center flex-1" :gutter="0">
                        <div class="flex flex-1">
                          <span class="pr-4 bord-r mr-4 pt-2 pb-2">
                            Applied Filter
                          </span>

                          <span
                            v-if="drillDownSeverity"
                            class="filter-alert-tag pt-2 pb-2"
                          >
                            <MTag
                              rounded
                              @close="drillDownSeverity = undefined"
                            >
                              {{ drillDownSeverity }}
                            </MTag>
                          </span>
                        </div>
                        <div>
                          <a @click="handleClear">Clear All</a>
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="shouldShowModule"
                      class="flex justify-between items-center ml-2"
                    >
                      <div>
                        <MInput
                          v-model="searchTerm"
                          class="search-box mr-4"
                          placeholder="Search"
                          name="search"
                        >
                          <template v-slot:prefix>
                            <MIcon name="search" />
                          </template>
                          <template v-if="searchTerm" v-slot:suffix>
                            <MIcon
                              name="times-circle"
                              class="text-neutral-light cursor-pointer"
                              @click="searchTerm = undefined"
                            />
                          </template>
                        </MInput>
                      </div>
                      <div class="flex flex-1 justify-end items-center">
                        <a @click.stop.prevent="severity = undefined">
                          <span class="text-primary mr-3">
                            {{ total }} Alerts
                          </span>
                        </a>
                        <SeveritySwitch
                          v-model="severity"
                          class="relative flex"
                          :severity-options="severityOptions"
                        />
                      </div>
                    </div>
                    <div class="flex flex-col flex-1 min-h-0">
                      <Transition name="placeholder" mode="in-out" appear>
                        <Grid
                          :key="gridRenderKey"
                          ref="alertGridRef"
                          :search-term="searchTerm"
                          :timeline="timeline"
                          :view="view"
                          :group-type="nestedTabId || tab"
                          :category="category"
                          :columns="persistedColumns"
                          :filters="appliedFilters"
                          :disable-declare-incident-drawer="true"
                          @declare-incident="
                            showDeclareIncidentDrawerFor = $event
                          "
                          @total="total = $event"
                          @column-change="updatePersistedColumns"
                          @counts="handleUpdateAlert"
                          @update-should-show-module="shouldShowModule = $event"
                          @incident-detail="showIncidentDetailsItem = $event"
                        />
                      </Transition>
                      <IncidentDetailsDrawer
                        v-if="showIncidentDetailsItem !== null"
                        :incident-item="showIncidentDetailsItem"
                        @hide="showIncidentDetailsItem = null"
                      />
                      <DeclareIncidentDrawer
                        v-if="showDeclareIncidentDrawerFor"
                        :alert="showDeclareIncidentDrawerFor"
                        @cancel="showDeclareIncidentDrawerFor = null"
                      />
                    </div>
                  </div>
                </div>
              </FlotoFixedView>
            </template>
          </MPersistedColumns>
        </GroupProvider>
      </PolicyProvider>
    </NetRouteProvider>
  </MonitorProvider>
</template>

<script>
import { authComputed } from '@state/modules/auth'

import { SEVERITY_MAP } from '@data/monitor'
import ColumnSelector from '@components/column-selector.vue'
import TimeRangePicker from '@components/widgets/time-range-picker.vue'
import MonitorProvider from '@components/data-provider/monitor-provider.vue'
import PolicyProvider from '@components/data-provider/policy-provider.vue'
import NetRouteProvider from '@components/data-provider/netroute-provider.vue'
import GroupProvider from '@components/data-provider/group-provider.vue'
import { UserPreferenceComputed } from '@state/modules/user-preference'
import { getColumns } from '../helpers/alert-helper'
import Filters from '../components/stream/filters.vue'
import SeveritySwitch from '../components/stream/severity-switch.vue'
import Grid from '../components/stream/grid.vue'
import Constants from '@constants'
import IncidentDetailsDrawer from '../components/stream/incident-details-drawer.vue'
import DeclareIncidentDrawer from '../components/stream/declare-incident-drawer.vue'

const PRE_APPLIED_SEVERITY_TO_CONSIDER = [
  Constants.DOWN,
  Constants.UNREACHABLE,
  Constants.CRITICAL,
  Constants.MAJOR,
  Constants.WARNING,
]

export default {
  name: 'Stream',
  page() {
    return {
      title: `Alert Stream`,
    }
  },
  components: {
    GroupProvider,
    TimeRangePicker,
    MonitorProvider,
    PolicyProvider,
    ColumnSelector,
    Grid,
    SeveritySwitch,
    Filters,
    IncidentDetailsDrawer,
    DeclareIncidentDrawer,
    NetRouteProvider,
  },
  data() {
    // this.viewOptions = [
    //   { value: 'live', label: 'Live View' },
    //   { value: 'flap', label: 'Flap View' },
    // ]
    this.monitorSearchParams = {
      category: [
        this.$constants.SERVER,
        this.$constants.NETWORK,
        this.$constants.SDN,
        this.$constants.OTHER,
        this.$constants.CLOUD,
        this.$constants.VIRTUALIZATION,
        this.$constants.SERVICE_CHECK,
        this.$constants.HYPERCONVERGED_INFRASTRUCTURE,
        this.$constants.STORAGE,
      ],
    }
    return {
      severityOptions: [],
      searchTerm: undefined,
      severity: PRE_APPLIED_SEVERITY_TO_CONSIDER,
      gridRenderKey: 1,
      total: 0,
      view: 'flap',
      tab: 'metric',
      category: 'Network',
      timeline: undefined,
      columns: [],
      isFilterVisible: false,
      filters: {},
      availableColumns: [],
      shouldShowModule: true,
      showIncidentDetailsItem: null,
      showDeclareIncidentDrawerFor: null,
      nestedTabId: this.$route.query.nestedTabId,
      policyType: undefined,
      drillDownSeverity: undefined,
      drillDownObjectType: undefined,
    }
  },
  computed: {
    ...UserPreferenceComputed,
    ...authComputed,
    appliedFilters() {
      let filters
      const value = this.filters
      if (value.monitors && value.monitors.length) {
        filters = [
          ...(filters || []),
          {
            field: 'monitor',
            operator: 'array_contains',
            value: value.monitors,
          },
        ]
      }
      if (value.policies && value.policies.length) {
        filters = [
          ...(filters || []),
          {
            field: 'policyId',
            operator: 'array_contains',
            value: value.policies,
          },
        ]
      }
      if (value.metrics && value.metrics.length) {
        filters = [
          ...(filters || []),
          {
            field: 'counterRawName',
            operator: 'array_contains',
            value: value.metrics,
          },
        ]
      }
      if (value.tags && value.tags.length) {
        filters = [
          ...(filters || []),
          {
            field: 'tag',
            operator: 'array_contains',
            value: value.tags,
          },
        ]
      }
      if (value.acknowledgeds && value.acknowledgeds.length) {
        filters = [
          ...(filters || []),
          {
            field: 'acknowledged',
            operator: 'eq',
            value: value.acknowledgeds === 'yes',
          },
        ]
      }
      if (value.severities && value.severities.length) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'array_contains',
            value: value.severities,
          },
        ]
      }
      if (this.severity) {
        if (this.severity === 'acknowledge') {
          filters = [
            ...(filters || []),
            {
              field: 'acknowledged',
              operator: 'eq',
              value: true,
            },
          ]
        } else {
          if (Array.isArray(this.severity)) {
            filters = [
              ...(filters || []),

              {
                field: 'severity',
                operator: 'array_contains',
                value: this.severity,
              },
            ]
          } else {
            filters = [
              ...(filters || []),
              {
                field: 'severity',
                operator: 'eq',
                value: this.severity,
              },
            ]
          }
        }
      }
      if (this.category && this.tab === 'metric') {
        filters = [
          ...(filters || []),
          {
            field: 'category',
            operator: 'eq',
            value: this.category,
          },
        ]
      }
      if (this.policyType && this.tab === 'metric') {
        filters = [
          ...(filters || []),
          {
            field: 'policyType',
            operator: 'eq',
            value: this.policyType,
          },
        ]
      }
      if (this.drillDownSeverity) {
        filters = [
          ...(filters || []),
          {
            field: 'severity',
            operator: 'eq',
            value: this.drillDownSeverity,
          },
        ]
      }
      if (this.drillDownObjectType) {
        filters = [
          ...(filters || []),
          {
            field: 'objectType',
            operator: 'eq',
            value: this.drillDownObjectType,
          },
        ]
      }

      return filters
    },
    hasLogFlowPermission() {
      return this.hasLicensePermission(
        this.$constants.LOG_FLOW_LICENSE_PERMISSION
      )
    },
    hasNetRoutePermission() {
      return this.hasLicensePermission(
        this.$constants.NETROUTE_LICENSE_PERMISSION
      )
    },
  },
  watch: {
    view(newValue, oldValue) {
      if (newValue !== oldValue) {
        const route = this.$router.resolve(
          this.$currentModule.getRoute('stream', {
            params: {
              ...this.$route.params,
            },
            query: {
              ...this.$route.query,
              view: newValue,
            },
          })
        )
        // if (route.href === this.$route.fullPath) {
        //   return
        // }
        this.shouldShowModule = true
        this.$router.push(route.href)
        this.filters = {}
        this.isFilterVisible = false
        this.availableColumns = getColumns(
          this.nestedTabId || this.tab,
          this.view,
          this.category
        )
        this.columns = getColumns(
          this.nestedTabId || this.tab,
          this.view,
          this.category
        )
      }
    },
    tab(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.shouldShowModule = true

        this.filters = {}
        this.isFilterVisible = false
        this.availableColumns = getColumns(
          this.nestedTabId || this.tab,
          this.view,
          this.category
        )
        this.columns = getColumns(
          this.nestedTabId || this.tab,
          this.view,
          this.category
        )
      }
    },
  },
  created() {
    const t = this.$route.query.t
    if (t) {
      try {
        this.timeline = JSON.parse(atob(decodeURIComponent(t)))
      } catch (e) {
        this.timeline = {
          selectedKey: 'today',
        }
      }
    } else {
      this.timeline = {
        selectedKey: 'today',
      }
    }
    this.tab = this.$route.params.tab
    this.nestedTabId = this.$route.query.nestedTabId
    this.view = ['live', 'flap'].includes((this.$route.query || {}).view)
      ? this.$route.query.view
      : 'flap'
    this.severity = Object.keys(SEVERITY_MAP)
      .map((s) => s.toLowerCase())
      .includes(((this.$route.query || {}).severity || '').toLowerCase())
      ? this.$route.query.severity.toUpperCase()
      : this.severity
    if (this.$route.query.stream === 'all') {
      this.category = null
    } else {
      this.category = this.$route.params.category
    }
    if (this.$route.params.policyType) {
      this.policyType = this.$route.params.policyType
    }
    if (this.$route.query?.drillDownSeverity) {
      this.drillDownSeverity =
        this.$route.query?.drillDownSeverity.toUpperCase()
    }
    if (this.$route.query?.drillDownObjectType) {
      this.drillDownObjectType = this.$route.query?.drillDownObjectType
    }
    this.columns = getColumns(
      this.nestedTabId || this.tab,
      this.view,
      this.category
    )
    this.availableColumns = getColumns(
      this.nestedTabId || this.tab,
      this.view,
      this.category
    )
  },
  methods: {
    handleClear() {
      this.$router.push(
        this.$currentModule.getRoute('stream', {
          params: {
            ...this.$route.params,
          },
          query: {
            ...this.$route.query,
            stream: 'all',
          },
        })
      )
      this.policyType = undefined
      this.drillDownSeverity = undefined
      this.drillDownObjectType = undefined
    },
    applyFilter() {
      this.isFilterVisible = false
    },
    refreshGrid() {
      this.gridRenderKey++
    },
    handleTabChange(tab, nestedTabId) {
      if (tab === 'netroute' && !nestedTabId) {
        return
      }
      this.$router.push(
        this.$currentModule.getRoute('stream', {
          params: {
            category: this.$route.params.category,
            tab,
          },
          query: {
            view: this.view,
            nestedTabId,
            ...(this.$route.query.stream === 'all'
              ? { stream: this.$route.query.stream }
              : {}),
          },
        })
      )
    },
    handleUpdateAlert(event) {
      if (this.tab === 'metric') {
        this.severityOptions = (event || []).filter(
          (c) => !['clear'].includes(c.value.toLowerCase())
        )
      } else {
        this.severityOptions = (event || []).filter((c) =>
          ['critical', 'major', 'warning'].includes(c.value.toLowerCase())
        )
      }
    },
    navigateToDashboard() {
      this.$router.push(
        this.$currentModule.getRoute('dashboard', {
          params: {
            type: this.tab,
          },
          query: {
            nestedTabId: this.nestedTabId,
          },
        })
      )
    },
    exportCsv() {
      this.$refs.alertGridRef.exportCsv()
    },
  },
}
</script>
